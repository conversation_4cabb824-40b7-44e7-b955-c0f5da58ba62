{"extends": "../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "composite": true, "incremental": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "references": [{"path": "../packages/bookmarked-types"}]}