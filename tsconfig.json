{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "commonjs", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "resolveJsonModule": true}, "exclude": ["node_modules", "dist", "build", "coverage"], "references": [{"path": "./packages/bookmarked-types"}, {"path": "./backend"}, {"path": "./frontend"}]}